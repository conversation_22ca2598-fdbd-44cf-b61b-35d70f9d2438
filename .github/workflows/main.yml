on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  run_pull:
    name: run pull
    runs-on: ubuntu-latest

    steps:
      - name: install ssh keys
        # check this thread to understand why its needed:
        # https://stackoverflow.com/a/70447517
        # pm2 start node --name "voiceform" -- -r dotenv/config build
        run: |
          install -m 600 -D /dev/null ~/.ssh/id_rsa
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          ssh-keyscan -p 22 -H ${{ vars.PROD_IP_ADDRESS }} > ~/.ssh/known_hosts
      - name: connect and pull
        run: >
          ssh -p 22 ${{ vars.SSH_USER }}@${{ vars.PROD_IP_ADDRESS }} "cd ${{ vars.WORK_DIR }} 
          && git checkout package.json 
          && git checkout ${{ vars.MAIN_BRANCH }} 
          && git pull && pnpm install --frozen-lockfile && pnpm run build 
          && pm2 restart voiceform && exit"
      - name: cleanup
        run: rm -rf ~/.ssh
