/** @type {import('tailwindcss').Config} */
export default {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	darkMode: 'class',
	theme: {
		extend: {
			colors: {
				// Dark mode colors (default/base)
				'audio-bg': '#0f0f0f',
				'audio-surface': '#1a1a1a',
				'audio-border': '#333333',
				'audio-text': '#ffffff',
				'audio-accent': '#00ff88',
				'audio-warning': '#ff4444',
				'audio-muted': '#888888',
				// Light mode colors
				'audio-bg-light': '#ffffff',
				'audio-surface-light': '#f8f9fa',
				'audio-border-light': '#e9ecef',
				'audio-text-light': '#1a1a1a',
				'audio-accent-light': '#00b366',
				'audio-warning-light': '#dc3545',
				'audio-muted-light': '#6c757d'
			}
		}
	},
	plugins: []
};
