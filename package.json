{"name": "srvai", "private": true, "version": "0.0.2", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "test:unit": "vitest", "test": "npm run test:unit -- --run"}, "devDependencies": {"@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.21.3", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "bits-ui": "^2.8.6", "clsx": "^2.1.1", "jsdom": "^26.1.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "svelte": "^5.33.18", "svelte-check": "^4.2.1", "svelte-sonner": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vaul-svelte": "1.0.0-next.7", "vite": "^6.3.5", "vitest": "^3.2.3"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"@sveltejs/adapter-node": "^5.2.12", "@types/audioworklet": "^0.0.76", "@types/web": "^0.0.239", "dotenv": "^16.5.0", "mode-watcher": "^1.0.8", "pnpm": "^10.12.1", "socket.io-client": "^4.8.1"}}