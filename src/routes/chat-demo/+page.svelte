<script lang="ts">
	import ChatDisplay from '$lib/vsurvey/ChatDisplay.svelte';

	// Sample chat messages following the ChatHistoryManager format
	let messages = [
		{
			role: 'user',
			message: 'Hello! Can you help me with something?'
		},
		{
			role: 'assistant',
			message: 'Of course! I\'d be happy to help you. What do you need assistance with?'
		},
		{
			role: 'user',
			message: 'I\'m trying to understand how this chat component works. Can you explain the features?'
		},
		{
			role: 'assistant',
			message: 'This chat component has several great features:\n\n• Clean, modern interface\n• Auto-scroll to bottom\n• Thinking indicator\n• Support for different message roles\n• Responsive design\n\nIt\'s built with Svelte 5 and styled with Tailwind CSS!'
		},
		{
			role: 'system',
			message: 'System message: This is how system messages appear'
		}
	];

	// Thinking state control
	let isThinking = false;

	// Demo functions
	function addUserMessage() {
		const userMessages = [
			'This is a new user message!',
			'How does the auto-scroll work?',
			'Can you show me the thinking indicator?',
			'This component looks great!',
			'Testing the message display...'
		];
		const randomMessage = userMessages[Math.floor(Math.random() * userMessages.length)];
		
		messages = [...messages, {
			role: 'user',
			message: randomMessage
		}];
	}

	function addAssistantMessage() {
		const assistantMessages = [
			'Thanks for testing the component!',
			'The auto-scroll works by detecting new messages and smoothly scrolling to the bottom.',
			'I can demonstrate various features of this chat interface.',
			'The component supports different message types and roles.',
			'Everything is working perfectly!'
		];
		const randomMessage = assistantMessages[Math.floor(Math.random() * assistantMessages.length)];
		
		messages = [...messages, {
			role: 'assistant',
			message: randomMessage
		}];
	}

	function toggleThinking() {
		isThinking = !isThinking;
	}

	function clearMessages() {
		messages = [];
	}

	function addSystemMessage() {
		messages = [...messages, {
			role: 'system',
			message: 'System: This is a system notification'
		}];
	}

	function simulateConversation() {
		// Add a user message
		addUserMessage();
		
		// Show thinking for 2 seconds
		setTimeout(() => {
			isThinking = true;
		}, 500);
		
		// Add assistant response after thinking
		setTimeout(() => {
			isThinking = false;
			addAssistantMessage();
		}, 2500);
	}
</script>

<svelte:head>
	<title>Chat Display Component Demo</title>
</svelte:head>

<div class="container mx-auto max-w-4xl p-6">
	<h1 class="mb-6 text-3xl font-bold text-gray-900">ChatDisplay Component Demo</h1>
	
	<div class="mb-6 rounded-lg bg-blue-50 p-4">
		<h2 class="mb-2 text-lg font-semibold text-blue-900">Component Features</h2>
		<ul class="list-disc list-inside space-y-1 text-blue-800">
			<li>Clean, modern chat interface with distinct user/assistant styling</li>
			<li>Auto-scroll to bottom when new messages arrive</li>
			<li>Animated thinking indicator with smooth transitions</li>
			<li>Support for ChatHistoryManager message format</li>
			<li>Responsive design with Tailwind CSS</li>
			<li>Bound variable for external thinking state control</li>
		</ul>
	</div>

	<div class="grid gap-6 lg:grid-cols-3">
		<!-- Chat Component -->
		<div class="lg:col-span-2">
			<h2 class="mb-4 text-xl font-semibold text-gray-900">Chat Display</h2>
			<ChatDisplay {messages} bind:thinking={isThinking} />
		</div>

		<!-- Controls -->
		<div class="space-y-4">
			<h2 class="text-xl font-semibold text-gray-900">Demo Controls</h2>
			
			<div class="space-y-2">
				<button
					on:click={addUserMessage}
					class="w-full rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
				>
					Add User Message
				</button>
				
				<button
					on:click={addAssistantMessage}
					class="w-full rounded-lg bg-green-500 px-4 py-2 text-white transition-colors hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
				>
					Add Assistant Message
				</button>
				
				<button
					on:click={addSystemMessage}
					class="w-full rounded-lg bg-gray-500 px-4 py-2 text-white transition-colors hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
				>
					Add System Message
				</button>
			</div>

			<div class="border-t pt-4">
				<button
					on:click={toggleThinking}
					class="w-full rounded-lg px-4 py-2 text-white transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 {isThinking ? 'bg-red-500 hover:bg-red-600 focus:ring-red-500' : 'bg-purple-500 hover:bg-purple-600 focus:ring-purple-500'}"
				>
					{isThinking ? 'Stop Thinking' : 'Start Thinking'}
				</button>
				
				<button
					on:click={simulateConversation}
					class="mt-2 w-full rounded-lg bg-indigo-500 px-4 py-2 text-white transition-colors hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
				>
					Simulate Conversation
				</button>
			</div>

			<div class="border-t pt-4">
				<button
					on:click={clearMessages}
					class="w-full rounded-lg bg-red-500 px-4 py-2 text-white transition-colors hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
				>
					Clear Messages
				</button>
			</div>

			<!-- Status Display -->
			<div class="rounded-lg bg-gray-100 p-3">
				<h3 class="font-semibold text-gray-900">Status</h3>
				<p class="text-sm text-gray-600">Messages: {messages.length}</p>
				<p class="text-sm text-gray-600">Thinking: {isThinking ? 'Yes' : 'No'}</p>
			</div>
		</div>
	</div>

	<!-- Usage Example -->
	<div class="mt-8 rounded-lg bg-gray-50 p-6">
		<h2 class="mb-4 text-xl font-semibold text-gray-900">Usage Example</h2>
		<pre class="overflow-x-auto rounded bg-gray-800 p-4 text-sm text-green-400"><code>{`<script>
  import ChatDisplay from '$lib/vsurvey/ChatDisplay.svelte';
  
  let messages = [
    { role: 'user', message: 'Hello!' },
    { role: 'assistant', message: 'Hi there! How can I help?' }
  ];
  let isThinking = false;
</script>

<ChatDisplay {messages} bind:thinking={isThinking} />`}</code></pre>
	</div>
</div>