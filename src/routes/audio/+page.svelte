<script lang="ts">
	import { onMount } from 'svelte';
	import { Play, Square, Settings, Activity } from '@lucide/svelte';
	import AudioMeter from '$lib/components/AudioMeter.svelte';
	import DeviceSelector from '$lib/components/DeviceSelector.svelte';
	import VolumeControl from '$lib/components/VolumeControl.svelte';
	import SpectrumAnalyzer from '$lib/components/SpectrumAnalyzer.svelte';
	import { audioManager } from '$lib/audioManager';
	import type { AudioSettings } from '$lib/audioManager';
	import ModeToggle from '$lib/components/ModeToggle.svelte';

	let isRecording = $state(false);
	let settings = $state<AudioSettings>({
		inputDeviceId: 'default',
		outputDeviceId: 'default',
		inputGain: 1.0,
		outputGain: 1.0,
		muted: false,
		sampleRate: 48000,
		bufferSize: 256
	});

	let showSettings = $state(false);
	let permissionsGranted = $state(false);
	let errorMessage = $state<string | null>(null);

	onMount(async () => {
		// Request permissions and initialize
		try {
			permissionsGranted = await audioManager.requestPermissions();
			if (!permissionsGranted) {
				errorMessage = 'Microphone permissions are required for audio monitoring.';
			}
			settings = audioManager.getSettings();
		} catch (error) {
			console.error('Failed to initialize audio:', error);
			errorMessage = 'Failed to initialize audio system.';
		}
	});

	$effect(() => {
		return () => {
			if (isRecording) {
				stopAudio();
			}
		};
	});

	async function startAudio() {
		try {
			errorMessage = null;
			const success = await audioManager.startAudioInput(settings.inputDeviceId);
			if (success) {
				isRecording = true;
			} else {
				errorMessage = 'Failed to start audio input.';
			}
		} catch (error) {
			console.error('Failed to start audio:', error);
			errorMessage = 'Failed to start audio input.';
		}
	}

	function stopAudio() {
		audioManager.stopAudioInput();
		isRecording = false;
	}

	function handleInputDeviceChange(deviceId: string) {
		settings.inputDeviceId = deviceId;
		if (isRecording) {
			// Restart with new device
			stopAudio();
			startAudio();
		}
	}

	function handleOutputDeviceChange(deviceId: string) {
		settings.outputDeviceId = deviceId;
	}

	function handleInputGainChange(gain: number) {
		settings.inputGain = gain;
		audioManager.setInputGain(gain);
	}

	function handleOutputGainChange(gain: number) {
		settings.outputGain = gain;
		audioManager.setOutputGain(gain);
	}

	function handleMuteToggle(muted: boolean) {
		settings.muted = muted;
		audioManager.setMuted(muted);
	}

	async function requestPermissions() {
		try {
			permissionsGranted = await audioManager.requestPermissions();
			if (permissionsGranted) {
				errorMessage = null;
			}
		} catch (error) {
			errorMessage = 'Failed to request microphone permissions.';
		}
	}
</script>

<svelte:head>
	<title>Audio Dashboard - Professional Audio Control Panel</title>
	<meta
		name="description"
		content="Complete browser-based audio control panel with real-time monitoring, device selection, and spectrum analysis."
	/>
</svelte:head>

<main class="min-h-screen bg-audio-bg dark:bg-audio-bg p-4 transition-colors">
	<div class="max-w-7xl mx-auto">
		<!-- Header -->
		<header class="flex items-center justify-between mb-8">
			<div class="flex items-center gap-3">
				<Activity class="w-8 h-8 text-audio-accent dark:text-audio-accent" />
				<div>
					<h1 class="text-3xl font-bold text-audio-text dark:text-audio-text">Audio Dashboard</h1>
					<p class="text-audio-muted dark:text-audio-muted">Professional Audio Control Panel</p>
				</div>
			</div>

			<div class="flex items-center gap-4">
				<ModeToggle />

				<button
					type="button"
					onclick={() => (showSettings = !showSettings)}
					class="p-2 rounded-lg bg-audio-surface dark:bg-audio-surface
					       border border-audio-border dark:border-audio-border
					       text-audio-text dark:text-audio-text
					       hover:bg-audio-border dark:hover:bg-audio-border
					       transition-colors"
					title="Settings"
				>
					<Settings class="w-5 h-5" />
				</button>

				{#if permissionsGranted}
					<button
						type="button"
						onclick={isRecording ? stopAudio : startAudio}
						class="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors {isRecording
							? 'bg-red-600 hover:bg-red-700 text-white'
							: 'bg-audio-accent dark:bg-audio-accent hover:bg-audio-accent/80 dark:hover:bg-audio-accent/80 text-black'}"
					>
						{#if isRecording}
							<Square class="w-4 h-4" />
							Stop Monitoring
						{:else}
							<Play class="w-4 h-4" />
							Start Monitoring
						{/if}
					</button>
				{:else}
					<button
						type="button"
						onclick={requestPermissions}
						class="px-4 py-2 rounded-lg bg-audio-accent dark:bg-audio-accent
						       hover:bg-audio-accent/80 dark:hover:bg-audio-accent/80
						       text-black font-medium transition-colors"
					>
						Grant Permissions
					</button>
				{/if}
			</div>
		</header>

		<!-- Error Message -->
		{#if errorMessage}
			<div
				class="mb-6 p-4 bg-red-900/20 dark:bg-red-100
			            border border-red-500/30 dark:border-red-300
			            rounded-lg text-red-400 dark:text-red-700"
			>
				{errorMessage}
			</div>
		{/if}

		<!-- Main Dashboard Grid -->
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Left Column - Controls -->
			<div class="space-y-6">
				<!-- Device Selection -->
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 transition-colors"
				>
					<h2 class="text-xl font-semibold text-audio-text dark:text-audio-text mb-4">
						Audio Devices
					</h2>
					<div class="space-y-4">
						<DeviceSelector
							type="input"
							selectedDeviceId={settings.inputDeviceId}
							onDeviceChange={handleInputDeviceChange}
						/>
						<DeviceSelector
							type="output"
							selectedDeviceId={settings.outputDeviceId}
							onDeviceChange={handleOutputDeviceChange}
						/>
					</div>
				</div>

				<!-- Volume Controls -->
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 transition-colors"
				>
					<h2 class="text-xl font-semibold text-audio-text dark:text-audio-text mb-4">
						Gain Controls
					</h2>
					<div class="space-y-6">
						<VolumeControl
							type="input"
							value={settings.inputGain}
							muted={settings.muted}
							onValueChange={handleInputGainChange}
							onMuteToggle={handleMuteToggle}
						/>
						<VolumeControl
							type="output"
							value={settings.outputGain}
							onValueChange={handleOutputGainChange}
						/>
					</div>
				</div>
			</div>

			<!-- Middle Column - Meters -->
			<div class="space-y-6">
				<!-- Level Meters -->
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 transition-colors"
				>
					<h2 class="text-xl font-semibold text-audio-text dark:text-audio-text mb-4">
						Audio Levels
					</h2>
					<div class="space-y-6">
						<!-- Horizontal Meters -->
						<div>
							<h3 class="text-sm font-medium text-audio-muted dark:text-audio-muted mb-2">
								Input Level
							</h3>
							<AudioMeter orientation="horizontal" size="large" />
						</div>

						<!-- Vertical Meters Side by Side -->
						<div class="flex gap-4 justify-center">
							<div class="text-center">
								<h3 class="text-sm font-medium text-audio-muted dark:text-audio-muted mb-2">L</h3>
								<AudioMeter orientation="vertical" size="medium" showNumbers={false} />
							</div>
							<div class="text-center">
								<h3 class="text-sm font-medium text-audio-muted dark:text-audio-muted mb-2">R</h3>
								<AudioMeter orientation="vertical" size="medium" showNumbers={false} />
							</div>
						</div>
					</div>
				</div>

				<!-- Quick Stats -->
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 transition-colors"
				>
					<h2 class="text-xl font-semibold text-audio-text dark:text-audio-text mb-4">
						System Info
					</h2>
					<div class="grid grid-cols-2 gap-4 text-sm">
						<div>
							<span class="text-audio-muted dark:text-audio-muted">Sample Rate:</span>
							<span class="text-audio-text dark:text-audio-text ml-2">{settings.sampleRate} Hz</span
							>
						</div>
						<div>
							<span class="text-audio-muted dark:text-audio-muted">Buffer Size:</span>
							<span class="text-audio-text dark:text-audio-text ml-2">{settings.bufferSize}</span>
						</div>
						<div>
							<span class="text-audio-muted dark:text-audio-muted">Status:</span>
							<span
								class="text-audio-text dark:text-audio-text ml-2 {isRecording
									? 'text-green-400 dark:text-green-600'
									: 'text-audio-muted dark:text-audio-muted'}"
							>
								{isRecording ? 'Active' : 'Inactive'}
							</span>
						</div>
						<div>
							<span class="text-audio-muted dark:text-audio-muted">Permissions:</span>
							<span
								class="text-audio-text dark:text-audio-text ml-2 {permissionsGranted
									? 'text-green-400 dark:text-green-600'
									: 'text-red-400 dark:text-red-600'}"
							>
								{permissionsGranted ? 'Granted' : 'Denied'}
							</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Right Column - Spectrum Analyzer -->
			<div class="space-y-6">
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 transition-colors"
				>
					<SpectrumAnalyzer width={350} height={250} />
				</div>

				<!-- Additional Controls -->
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 transition-colors"
				>
					<h2 class="text-xl font-semibold text-audio-text dark:text-audio-text mb-4">Analysis</h2>
					<div class="space-y-4">
						<div class="grid grid-cols-2 gap-4">
							<button
								class="px-3 py-2 bg-audio-border dark:bg-audio-border
								       hover:bg-audio-muted dark:hover:bg-audio-muted
								       text-audio-text dark:text-audio-text
								       rounded transition-colors text-sm"
								onclick={() => {}}
							>
								Reset Peak Hold
							</button>
							<button
								class="px-3 py-2 bg-audio-border dark:bg-audio-border
								       hover:bg-audio-muted dark:hover:bg-audio-muted
								       text-audio-text dark:text-audio-text
								       rounded transition-colors text-sm"
								onclick={() => {}}
							>
								Calibrate
							</button>
						</div>

						<div class="space-y-2">
							<label
								for="analyzer-settings"
								class="block text-sm text-audio-muted dark:text-audio-muted"
								>Analyzer Settings</label
							>
							<select
								id="analyzer-settings"
								class="w-full px-3 py-2
							                                       bg-audio-border dark:bg-audio-border
							                                       border border-audio-border dark:border-audio-border
							                                       text-audio-text dark:text-audio-text
							                                       rounded text-sm"
							>
								<option>Log Scale</option>
								<option>Linear Scale</option>
							</select>
						</div>
					</div>
				</div>

				<!-- Presets -->
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 transition-colors"
				>
					<h2 class="text-xl font-semibold text-audio-text dark:text-audio-text mb-4">Presets</h2>
					<button
						onclick={() => {
							console.log('Button clicked!');
						}}
						type="button"
						>asdfasdf
					</button>
					<div class="space-y-2">
						<button
							class="w-full px-3 py-2 bg-audio-border dark:bg-audio-border
						               hover:bg-audio-muted dark:hover:bg-audio-muted
						               text-audio-text dark:text-audio-text
						               rounded transition-colors text-sm text-left"
						>
							🎤 Vocal Recording
						</button>
						<button
							class="w-full px-3 py-2 bg-audio-border dark:bg-audio-border
						               hover:bg-audio-muted dark:hover:bg-audio-muted
						               text-audio-text dark:text-audio-text
						               rounded transition-colors text-sm text-left"
						>
							🎵 Music Playback
						</button>
						<button
							class="w-full px-3 py-2 bg-audio-border dark:bg-audio-border
						               hover:bg-audio-muted dark:hover:bg-audio-muted
						               text-audio-text dark:text-audio-text
						               rounded transition-colors text-sm text-left"
						>
							🎙️ Podcast Setup
						</button>
						<button
							class="w-full px-3 py-2 bg-audio-border dark:bg-audio-border
						               hover:bg-audio-muted dark:hover:bg-audio-muted
						               text-audio-text dark:text-audio-text
						               rounded transition-colors text-sm text-left"
						>
							🔧 Custom Settings
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Settings Panel -->
		{#if showSettings}
			<div class="fixed inset-0 bg-black/50 dark:bg-black/30 flex items-center justify-center z-50">
				<div
					class="bg-audio-surface dark:bg-audio-surface
				            border border-audio-border dark:border-audio-border
				            rounded-xl p-6 max-w-md w-full mx-4 transition-colors"
				>
					<div class="flex items-center justify-between mb-4">
						<h3 class="text-lg font-semibold text-audio-text dark:text-audio-text">
							Advanced Settings
						</h3>
						<button
							onclick={() => (showSettings = false)}
							class="text-audio-muted dark:text-audio-muted
							       hover:text-audio-text dark:hover:text-audio-text
							       transition-colors"
						>
							✕
						</button>
					</div>

					<div class="space-y-4">
						<div>
							<label
								for="sample-rate"
								class="block text-sm text-audio-muted dark:text-audio-muted mb-1">Sample Rate</label
							>
							<select
								id="sample-rate"
								bind:value={settings.sampleRate}
								class="w-full px-3 py-2 bg-audio-border dark:bg-audio-border
								       border border-audio-border dark:border-audio-border
								       text-audio-text dark:text-audio-text
								       rounded"
							>
								<option value={44100}>44.1 kHz</option>
								<option value={48000}>48 kHz</option>
								<option value={96000}>96 kHz</option>
							</select>
						</div>

						<div>
							<label
								for="buffer-size"
								class="block text-sm text-audio-muted dark:text-audio-muted mb-1">Buffer Size</label
							>
							<select
								id="buffer-size"
								bind:value={settings.bufferSize}
								class="w-full px-3 py-2 bg-audio-border dark:bg-audio-border
								       border border-audio-border dark:border-audio-border
								       text-audio-text dark:text-audio-text
								       rounded"
							>
								<option value={128}>128 samples</option>
								<option value={256}>256 samples</option>
								<option value={512}>512 samples</option>
								<option value={1024}>1024 samples</option>
							</select>
						</div>

						<button
							onclick={() => (showSettings = false)}
							class="w-full px-4 py-2 bg-audio-accent dark:bg-audio-accent
							       text-black rounded font-medium
							       hover:bg-audio-accent/80 dark:hover:bg-audio-accent/80
							       transition-colors"
						>
							Apply Settings
						</button>
					</div>
				</div>
			</div>
		{/if}

		<!-- Footer -->
		<footer class="mt-12 text-center text-audio-muted dark:text-audio-muted text-sm">
			<p>Audio Dashboard v1.0 | Built with SvelteKit, TypeScript & TailwindCSS</p>
			<p class="mt-1">Modern Web Audio API - No deprecated methods</p>
		</footer>
	</div>
</main>
