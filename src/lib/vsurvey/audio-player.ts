const AudioPlayerWorkletUrl = new URL(
	'./AudioPlayerProcessor.worklet.js',
	import.meta.url
).toString();


export class AudioPlayer {
	private onAudioPlayedListeners: Array<any> = [];
	private initialized: boolean = false;

	private audioContext: AudioContext | null = null;
	private analyser: AnalyserNode | null = null;
	private workletNode: AudioWorkletNode | null = null;
	private monitorWorkletNode: AudioWorkletNode | null = null; // Replaces ScriptProcessorNode
	private initialBufferLength: number = 0;
	private initialBufferIndex: number = 0;
	private initialBufferSent: boolean = false;
	private initialBufferSentListeners: Array<any> = [];
	private initialBufferSentListenersIndex: number = 0;
	private initialBufferSentListenersCount: number = 0;
	private initialBufferSentListenersDone: boolean = false;
	private initialBufferSentListenersDoneListeners: Array<any> = [];
	private initialBufferSentListenersDoneListenersIndex: number = 0;

	addEventListener(event: any, callback: any) {
		switch (event) {
			case 'onAudioPlayed':
				this.onAudioPlayedListeners.push(callback);
				break;
			default:
				console.error(
					'Listener registered for event type: ' + JSON.stringify(event) + ' which is not supported'
				);
		}
	}

	async start() {
		this.audioContext = new AudioContext({ sampleRate: 24000 });
		this.analyser = this.audioContext.createAnalyser();
		this.analyser.fftSize = 512;

		// Load both worklet modules
		await Promise.all([
			this.audioContext.audioWorklet.addModule(AudioPlayerWorkletUrl),
			this.loadMonitorWorklet()
		]);

		// Create main audio worklet
		this.workletNode = new AudioWorkletNode(this.audioContext, 'audio-player-processor');
		
		// Create monitor worklet to replace ScriptProcessorNode
		this.monitorWorkletNode = new AudioWorkletNode(this.audioContext, 'audio-monitor-processor', {
			numberOfInputs: 1,
			numberOfOutputs: 1,
			channelCount: 1,
			channelCountMode: 'explicit',
			channelInterpretation: 'speakers'
		});

		// Handle messages from monitor worklet
		this.monitorWorkletNode.port.onmessage = (event) => {
			const { type, audioData } = event.data;
			if (type === 'audio-played') {
				// Notify listeners that audio was played
				const samples = new Float32Array(audioData);
				this.onAudioPlayedListeners.forEach(listener => listener(samples));
			}
		};

		// Connect audio graph: worklet -> monitor -> analyser -> destination
		this.workletNode.connect(this.monitorWorkletNode);
		this.monitorWorkletNode.connect(this.analyser);
		this.analyser.connect(this.audioContext.destination);

		this.#maybeOverrideInitialBufferLength();
		this.initialized = true;
	}

	private async loadMonitorWorklet() {
		// Create the monitor worklet code as a blob URL
		const workletCode = `
			class AudioMonitorProcessor extends AudioWorkletProcessor {
				process(inputs, outputs, parameters) {
					const input = inputs[0];
					const output = outputs[0];
					
					if (input.length > 0 && output.length > 0) {
						// Pass audio through unchanged
						for (let channel = 0; channel < input.length; channel++) {
							const inputChannel = input[channel];
							const outputChannel = output[channel];
							outputChannel.set(inputChannel);
						}
						
						// Send audio data to main thread for listeners
						if (input[0] && input[0].length > 0) {
							this.port.postMessage({
								type: 'audio-played',
								audioData: input[0] // Send first channel
							});
						}
					}
					
					return true;
				}
			}
			
			registerProcessor('audio-monitor-processor', AudioMonitorProcessor);
		`;

		const blob = new Blob([workletCode], { type: 'application/javascript' });
		const workletUrl = URL.createObjectURL(blob);
		
		try {
			await this.audioContext!.audioWorklet.addModule(workletUrl);
		} finally {
			// Clean up the blob URL
			URL.revokeObjectURL(workletUrl);
		}
	}

	bargeIn() {
		if (this.workletNode) {
			this.workletNode.port.postMessage({
				type: 'barge-in'
			});
		}
	}

	stop() {
		if (this.audioContext) {
			this.audioContext?.close();
		}

		if (this.analyser) {
			this.analyser?.disconnect();
		}

		if (this.workletNode) {
			this.workletNode?.disconnect();
		}

		if (this.monitorWorkletNode) {
			this.monitorWorkletNode?.disconnect();
		}

		this.initialized = false;
		this.audioContext = null;
		this.analyser = null;
		this.workletNode = null;
		this.monitorWorkletNode = null;
	}

	// # means really private at runtime
	#maybeOverrideInitialBufferLength() {
		// Read a user-specified initial buffer length from the URL parameters to help with tinkering
		const params = new URLSearchParams(window.location.search);
		const value = params.get('audioPlayerInitialBufferLength');
		if (value === null) {
			return; // No override specified
		}
		const bufferLength = parseInt(value);
		if (isNaN(bufferLength)) {
			console.error('Invalid audioPlayerInitialBufferLength value:', JSON.stringify(value));
			return;
		}
		this.workletNode?.port.postMessage({
			type: 'initial-buffer-length',
			bufferLength: bufferLength
		});
	}

	playAudio(samples: any) {
		if (!this.initialized) {
			console.error(
				'The audio player is not initialized. Call start() before attempting to play audio.'
			);
			return;
		}
		this.workletNode?.port.postMessage({
			type: 'audio',
			audioData: samples
		});
	}

	getSamples() {
		if (!this.initialized) {
			return null;
		}
		if (this.analyser) {
			const bufferLength = this.analyser.frequencyBinCount;
			const dataArray = new Uint8Array(bufferLength);
			this.analyser.getByteTimeDomainData(dataArray);
			return [...dataArray].map((e) => e / 128 - 1);
		} else {
			return null;
		}
	}

	getVolume() {
		if (!this.initialized || null == this.analyser) {
			return 0;
		}
		const bufferLength = this.analyser.frequencyBinCount;
		const dataArray = new Uint8Array(bufferLength);
		this.analyser.getByteTimeDomainData(dataArray);
		let normSamples = [...dataArray].map((e) => e / 128 - 1);
		let sum = 0;
		for (let i = 0; i < normSamples.length; i++) {
			sum += normSamples[i] * normSamples[i];
		}
		return Math.sqrt(sum / normSamples.length);
	}
}