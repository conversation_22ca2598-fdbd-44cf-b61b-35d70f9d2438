<script lang="ts">
	import { onMount } from 'svelte';
    import { type ChatMessage} from './types';

	// Component props
    let {
        thinking = $bindable(),
        listening = $bindable(),
        messages = $bindable()
    } = $props<{
        thinking: boolean;
		listening: boolean
        messages: ChatMessage[];
    }>();

	// Local variables
	let chatContainer: HTMLDivElement;
	let shouldAutoScroll = $state(true);

	// Auto-scroll to bottom when messages change or thinking state changes
	$effect(() => {
		if (messages || thinking) {
			scrollToBottom();
		}
	});

	function scrollToBottom() {
		if (chatContainer && shouldAutoScroll) {
			setTimeout(() => {
				chatContainer.scrollTop = chatContainer.scrollHeight;
			}, 10);
		}
	}

	// Check if user has scrolled up manually
	function handleScroll() {
		if (chatContainer) {
			const { scrollTop, scrollHeight, clientHeight } = chatContainer;
			shouldAutoScroll = scrollTop + clientHeight >= scrollHeight - 10;
		}
	}

	// Ensure we scroll to bottom on mount
	onMount(() => {
		scrollToBottom();
	});

	// Format message content with line breaks
	function formatMessage(content: string): string {
		return content.replace(/\n/g, '<br>');
	}

	// Get message styling classes based on role
	function getMessageClasses(role: string): string {
		switch (role.toLowerCase()) {
			case 'user':
				return 'ml-auto bg-blue-500 text-white';
			case 'assistant':
				return 'mr-auto bg-gray-100 text-gray-900';
			case 'system':
				return 'mx-auto bg-gray-50 text-gray-600 text-center italic';
			default:
				return 'mr-auto bg-gray-100 text-gray-900';
		}
	}

	// Get container classes based on role
	function getContainerClasses(role: string): string {
		switch (role.toLowerCase()) {
			case 'user':
				return 'flex justify-end';
			case 'system':
				return 'flex justify-center';
			default:
				return 'flex justify-start';
		}
	}
</script>

<div
	class="flex h-96 w-full flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm"
>
	<!-- Chat messages container -->
	<div
		bind:this={chatContainer}
		onscroll={handleScroll}
		class="flex-1 space-y-4 overflow-y-auto p-4 scroll-smooth"
	>
		<!-- Render messages -->
		{#each messages as message, index (index)}
			<div class={getContainerClasses(message.role)}>
				<div
					class={`max-w-xs rounded-lg px-4 py-2 text-sm leading-relaxed sm:max-w-md ${getMessageClasses(message.role)}`}
				>
					{@html formatMessage(message.message)}
				</div>
			</div>
		{/each}

		<!-- Thinking indicator -->
		{#if thinking}
			<div class="flex justify-start">
				<div
					class="max-w-xs rounded-lg bg-gray-100 px-4 py-2 text-gray-900 sm:max-w-md"
				>
					<div class="flex items-center space-x-1">
						<span class="text-sm text-gray-500">Thinking</span>
						<div class="flex space-x-1">
							<div class="thinking-dot"></div>
							<div class="thinking-dot" style="animation-delay: 0.2s;"></div>
							<div class="thinking-dot" style="animation-delay: 0.4s;"></div>
						</div>
					</div>
				</div>
			</div>
		{/if}
<!-- Listening indicator -->
		{#if listening}
			<div class="flex justify-end">
				<div
					class="max-w-xs rounded-lg bg-gray-100 px-4 py-2 text-gray-900 sm:max-w-md"
				>
					<div class="flex items-center space-x-1">
						<span class="text-sm text-gray-500">Listening</span>
						<div class="flex space-x-1">
							<div class="thinking-dot"></div>
							<div class="thinking-dot" style="animation-delay: 0.2s;"></div>
							<div class="thinking-dot" style="animation-delay: 0.4s;"></div>
						</div>
					</div>
				</div>
			</div>
		{/if}

		<!-- End of conversation marker -->
		{#if messages.some((msg: { endOfConversation: any; }) => msg.endOfConversation)}
			<div class="flex justify-center">
				<div class="rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-500">
					End of conversation
				</div>
			</div>
		{/if}
	</div>

	<!-- Scroll to bottom button (appears when user scrolls up) -->
	{#if !shouldAutoScroll}
		<div class="absolute bottom-4 right-4">
			<button
				onclick={() => {
					shouldAutoScroll = true;
					scrollToBottom();
				}}
				class="rounded-full bg-blue-500 p-2 text-white shadow-lg transition-all hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
				aria-label="Scroll to bottom"
			>
				<svg
					class="h-4 w-4"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M19 14l-7 7m0 0l-7-7m7 7V3"
					></path>
				</svg>
			</button>
		</div>
	{/if}
</div>

<style>
	.thinking-dot {
		width: 4px;
		height: 4px;
		background-color: #9ca3af;
		border-radius: 50%;
		animation: thinking-pulse 1.4s ease-in-out infinite both;
	}

	@keyframes thinking-pulse {
		0%,
		80%,
		100% {
			transform: scale(0);
			opacity: 0.5;
		}
		40% {
			transform: scale(1);
			opacity: 1;
		}
	}

	/* Custom scrollbar styling */
	.overflow-y-auto::-webkit-scrollbar {
		width: 6px;
	}

	.overflow-y-auto::-webkit-scrollbar-track {
		background: #f1f5f9;
		border-radius: 3px;
	}

	.overflow-y-auto::-webkit-scrollbar-thumb {
		background: #cbd5e1;
		border-radius: 3px;
	}

	.overflow-y-auto::-webkit-scrollbar-thumb:hover {
		background: #94a3b8;
	}
</style>