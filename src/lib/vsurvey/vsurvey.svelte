<script lang="ts">
	import { AudioPlayer } from './audio-player';
	import ChatDisplay from '$lib/vsurvey/ChatDisplay.svelte';
	import { onMount } from 'svelte';
	import { arrayBufferToBase64, base64ToFloat32Array } from './util';
	import { io, Socket } from 'socket.io-client';
	import { PUBLIC_S2S_SERVER } from '$env/static/public';
	import type { ChatMessage } from './types';



	// Component props
    let { system_prompt  } = $props<{
	     system_prompt: string;
    }>();

	let socket: Socket;

	onMount(() => {
		// Connect to the server
		socket = io(PUBLIC_S2S_SERVER);
		//socket = io.connect("http://********:5000");
		InitEventHandlers();
		initAudio();
		const r = socket.connect();
	
		// Optional return function
		return () => {
			// This is executed when the component is removed from the DOM
			socket.disconnect();
		};
	});

	let statusText = $state('Disconnected');
	let statusClass = $state('disconnected');
	let startButtonEnabled = $state(false);
	let stopButtonEnabled = $state(false);

	// chat display bound vars
	let chatHistory: ChatMessage[] = $state([]);
	let thinking = $state(false);
	let listening = $state(false);

	function addTextMessage(content: { role: any; message: string }) {
		let updatedChatHistory = [...chatHistory];
		let lastTurn = updatedChatHistory[updatedChatHistory.length - 1];

		if (lastTurn !== undefined && lastTurn.role === content.role) {
			// Same role, append to the last turn
			updatedChatHistory[updatedChatHistory.length - 1] = {
				...content,
				message: lastTurn.message + ' ' + content.message
			};
		} else {
			// Different role, add a new turn
			updatedChatHistory.push({
				role: content.role,
				message: content.message
			});
		}
		chatHistory = updatedChatHistory;
	}

	function endTurn() {
		let updatedChatHistory = chatHistory.map((item) => {
			return {
				...item,
				endOfResponse: true
			};
		});

		chatHistory = updatedChatHistory;
	}

	function endConversation() {
		let updatedChatHistory = chatHistory.map((item) => {
			return {
				...item,
				endOfResponse: true
			};
		});

		updatedChatHistory.push({
			role: '',
			message: '',
			endOfResponse: true,
			endOfConversation: true
		});

		chatHistory = updatedChatHistory;
	}

	// Audio processing variables
	let audioContext: AudioContext;
	let audioStream: MediaStream;
	let isStreaming = false;
	//let processor:ScriptProcessorNode;
	let microphoneWorkletNode: AudioWorkletNode | null = null; // Replaces ScriptProcessorNode
	let sourceNode: MediaStreamAudioSourceNode;
	let transcriptionReceived = false;
	let displayAssistantText = false;
	let role: string;
	const audioPlayer = new AudioPlayer();
	let sessionInitialized = false;

	let samplingRatio = 1;
	const TARGET_SAMPLE_RATE = 16000;
	const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');







	// Custom system prompt - you can modify this
	/*let SYSTEM_PROMPT =
		'You are a friend. The user and you will engage in a spoken ' +
		'dialog exchanging the transcripts of a natural real-time conversation. Keep your responses short, ' +
		'generally two or three sentences for chatty scenarios.';
	*/
	const SYSTEM_PROMPT = `You are a survey agent. The user and you will engage in a spoken 
  conversation where you will ask the user a series of questions to gather information.
  Keep your responses short, generally two or three sentences for chatty scenarios.
  When the user is ready to begin, you will introduce yourself and ask the first question.
  Use the tool to get the questions to ask the user.  Do not ask any questions that are not provided by the tool.
  Always start by using getNextQuestionTool to get the first question.
  `;


// Create microphone worklet processor
	async function createMicrophoneWorklet() {
		const workletCode = `
			class MicrophoneProcessor extends AudioWorkletProcessor {
				constructor() {
					super();
					this.isStreaming = false;
					this.samplingRatio = 1;
					this.isFirefox = false;
					
					// Listen for configuration updates
					this.port.onmessage = (event) => {
						const { type, data } = event.data;
						if (type === 'updateConfig') {
							this.isStreaming = data.isStreaming;
							this.samplingRatio = data.samplingRatio;
							this.isFirefox = data.isFirefox;
						}
					};
				}

				process(inputs, outputs, parameters) {
					if (!this.isStreaming || !inputs[0] || !inputs[0][0]) {
						return true;
					}

					const inputData = inputs[0][0]; // Get first channel
					const numSamples = Math.round(inputData.length / this.samplingRatio);
					const pcmData = this.isFirefox ? new Int16Array(numSamples) : new Int16Array(inputData.length);

					// Convert to 16-bit PCM
					if (this.isFirefox) {
						for (let i = 0; i < numSamples; i++) {
							// NOTE: for firefox the samplingRatio is not 1,
							// so it will downsample by skipping some input samples
							// A better approach is to compute the mean of the samplingRatio samples.
							// or pass through a low-pass filter first
							// But skipping is a preferable low-latency operation
							const sampleIndex = Math.floor(i * this.samplingRatio);
							if (sampleIndex < inputData.length) {
								pcmData[i] = Math.max(-1, Math.min(1, inputData[sampleIndex])) * 0x7fff;
							}
						}
					} else {
						for (let i = 0; i < inputData.length; i++) {
							pcmData[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7fff;
						}
					}

					// Send PCM data to main thread
					this.port.postMessage({
						type: 'audioData',
						data: pcmData.buffer
					});

					return true;
				}
			}

			registerProcessor('microphone-processor', MicrophoneProcessor);
		`;

		const blob = new Blob([workletCode], { type: 'application/javascript' });
		const workletUrl = URL.createObjectURL(blob);
		
		try {
			await audioContext.audioWorklet.addModule(workletUrl);
		} finally {
			URL.revokeObjectURL(workletUrl);
		}
	}



  
	// Initialize WebSocket audio
	async function initAudio() {
		try {
			statusText = 'Requesting microphone access...';
			statusClass = 'connecting';

			// Request microphone access
			audioStream = await navigator.mediaDevices.getUserMedia({
				audio: {
					echoCancellation: true,
					noiseSuppression: true,
					autoGainControl: true
				}
			});

			if (isFirefox) {
				//firefox doesn't allow audio context have differnt sample rate than what the user media device offers
				audioContext = new AudioContext();
				
			} else {
				audioContext = new AudioContext({
					sampleRate: TARGET_SAMPLE_RATE
				});
			}
			
			//samplingRatio - is only relevant for firefox, for Chromium based browsers, it's always 1
			samplingRatio = audioContext.sampleRate / TARGET_SAMPLE_RATE;
			console.log(
				`Debug AudioContext- sampleRate: ${audioContext.sampleRate} samplingRatio: ${samplingRatio}`
			);

			// Load microphone worklet
			await createMicrophoneWorklet();
			await audioPlayer.start();

			statusText = 'Microphone ready. Click Start to begin.';
			statusClass = 'ready';
			startButtonEnabled = true;
		} catch (error: any) {
			console.error('Error accessing microphone:', error);
			statusText = 'Error: ' + error.message;
			statusClass = 'error';
		}
	}

	// Initialize the session with Bedrock
	async function initializeSession() {
		if (sessionInitialized) return;

		statusText = 'Initializing session...';

		try {
			// Send events in sequence
			socket.emit('promptStart');
			socket.emit('systemPrompt', system_prompt || SYSTEM_PROMPT);
			socket.emit('audioStart');

			// Mark session as initialized
			sessionInitialized = true;
			statusText = 'Session initialized successfully';
		} catch (error) {
			console.error('Failed to initialize session:', error);
			statusText = 'Error initializing session';
			statusClass = 'error';
		}
	}

	async function startStreaming() {
		if (isStreaming) return;

		try {
			// First, make sure the session is initialized
			if (!sessionInitialized) {
				await initializeSession();
			}

			// Create audio source
			sourceNode = audioContext.createMediaStreamSource(audioStream);

			// Create modern AudioWorkletNode instead of deprecated ScriptProcessorNode
			microphoneWorkletNode = new AudioWorkletNode(audioContext, 'microphone-processor', {
				numberOfInputs: 1,
				numberOfOutputs: 0, // No output needed for microphone processing
				channelCount: 1,
				channelCountMode: 'explicit',
				channelInterpretation: 'speakers'
			});

			// Handle messages from worklet
			microphoneWorkletNode.port.onmessage = (event) => {
				const { type, data } = event.data;
				if (type === 'audioData') {
					// Convert to base64 (browser-safe way)
					const base64Data = arrayBufferToBase64(data);
					
					// Send to server
					socket.emit('audioInput', base64Data);
				}
			};

			// Configure worklet
			microphoneWorkletNode.port.postMessage({
				type: 'updateConfig',
				data: {
					isStreaming: true,
					samplingRatio: samplingRatio,
					isFirefox: isFirefox
				}
			});

			// Connect audio graph
			sourceNode.connect(microphoneWorkletNode);

			isStreaming = true;
			startButtonEnabled = false;
			stopButtonEnabled = true;
			statusText = 'Streaming... Speak now';
			statusClass = 'recording';

			// Show user thinking/listening indicator when starting to record
			transcriptionReceived = false;
			listening = true;
		} catch (error: any) {
			console.error('Error starting recording:', error);
			statusText = 'Error: ' + error.message;
			statusClass = 'error';
			
			// Fallback to legacy implementation if AudioWorklet fails
			console.warn('AudioWorklet failed, falling back to ScriptProcessorNode');
			await startStreamingLegacy();
		}
	}

	// Fallback legacy implementation
	async function startStreamingLegacy() {
		try {
			if (!sessionInitialized) {
				await initializeSession();
			}

			sourceNode = audioContext.createMediaStreamSource(audioStream);

			// Fallback to ScriptProcessorNode for older browsers
			if (audioContext.createScriptProcessor) {
				const processor = audioContext.createScriptProcessor(512, 1, 1);

				processor.onaudioprocess = (e) => {
					if (!isStreaming) return;

					const inputData = e.inputBuffer.getChannelData(0);
					const numSamples = Math.round(inputData.length / samplingRatio);
					const pcmData = isFirefox ? new Int16Array(numSamples) : new Int16Array(inputData.length);

					// Convert to 16-bit PCM
					if (isFirefox) {
						for (let i = 0; i < inputData.length; i++) {
							pcmData[i] = Math.max(-1, Math.min(1, inputData[i * samplingRatio])) * 0x7fff;
						}
					} else {
						for (let i = 0; i < inputData.length; i++) {
							pcmData[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7fff;
						}
					}

					// Convert to base64 (browser-safe way)
					const base64Data = arrayBufferToBase64(pcmData.buffer);

					// Send to server
					socket.emit('audioInput', base64Data);
				};

				sourceNode.connect(processor);
				processor.connect(audioContext.destination);
			}

			isStreaming = true;
			startButtonEnabled = false;
			stopButtonEnabled = true;
			statusText = 'Streaming... Speak now (legacy mode)';
			statusClass = 'recording';
			transcriptionReceived = false;
			listening = true;
		} catch (error: any) {
			console.error('Error starting legacy recording:', error);
			statusText = 'Error: ' + error.message;
			statusClass = 'error';
		}
	}

	function stopStreaming() {
		if (!isStreaming) return;

		isStreaming = false;

		// Clean up audio processing
		if (microphoneWorkletNode) {
			// Stop streaming in worklet
			microphoneWorkletNode.port.postMessage({
				type: 'updateConfig',
				data: {
					isStreaming: false,
					samplingRatio: samplingRatio,
					isFirefox: isFirefox
				}
			});
			microphoneWorkletNode.disconnect();
			microphoneWorkletNode = null;
		}

		if (sourceNode) {
			sourceNode.disconnect();
		}

		startButtonEnabled = true;
		stopButtonEnabled = false;
		statusText = 'Processing...';
		statusClass = 'processing';

		audioPlayer.stop();
		// Tell server to finalize processing
		socket.emit('stopAudio');

		// End the current turn in chat history
		endTurn();
	}


	

	// Process message data and add to chat history
	function handleTextOutput(data: { role: any; content: any; }) {
		console.log('Processing text output:', data);
		if (data.content) {
			const messageData = {
				role: data.role,
				message: data.content
			};
			addTextMessage(messageData);
		}
	}

	// EVENT HANDLERS
	// --------------
	function InitEventHandlers() {
		// Handle content start from the server
		socket.on('contentStart', (data) => {
			console.log('Content start received:', data);

			if (data.type === 'TEXT') {
				// Below update will be enabled when role is moved to the contentStart
				role = data.role;
				if (data.role === 'USER') {
					// When user's text content starts, hide user thinking indicator
					listening = false;
				} else if (data.role === 'ASSISTANT') {
					// When assistant's text content starts, hide assistant thinking indicator
					thinking = false;
					let isSpeculative = false;
					try {
						if (data.additionalModelFields) {
							const additionalFields = JSON.parse(data.additionalModelFields);
							isSpeculative = additionalFields.generationStage === 'SPECULATIVE';
							if (isSpeculative) {
								console.log('Received speculative content');
								displayAssistantText = true;
							} else {
								displayAssistantText = false;
							}
						}
					} catch (e) {
						console.error('Error parsing additionalModelFields:', e);
					}
				}
			} else if (data.type === 'AUDIO') {
				// When audio content starts, we may need to show user thinking/listening indicator
				if (isStreaming) {
					listening = true;
				}
			}
		});

		// Handle text output from the server
		socket.on('textOutput', (data) => {
			console.log('Received text output:', data);

			if (role === 'USER') {
				// When user text is received, show thinking indicator for assistant response
				transcriptionReceived = true;
				//listening = false();

				// Add user message to chat
				handleTextOutput({
					role: data.role,
					content: data.content
				});

				// Show assistant thinking indicator after user text appears
				thinking = true;
			} else if (role === 'ASSISTANT') {
				//thinkng = false();
				if (displayAssistantText) {
					handleTextOutput({
						role: data.role,
						content: data.content
					});
				}
			}
		});

		// Handle audio output
		socket.on('audioOutput', (data) => {
			if (data.content) {
				try {
					const audioData = base64ToFloat32Array(data.content);
					audioPlayer.playAudio(audioData);
				} catch (error) {
					console.error('Error processing audio data:', error);
				}
			}
		});

		// Handle content end events
		socket.on('contentEnd', (data) => {
			console.log('Content end received:', data);

			if (data.type === 'TEXT') {
				if (role === 'USER') {
					// When user's text content ends, make sure assistant thinking is shown
					listening = false;
					thinking = true;
				} else if (role === 'ASSISTANT') {
					// When assistant's text content ends, prepare for user input in next turn
					thinking = false;
				}

				// Handle stop reasons
				if (data.stopReason && data.stopReason.toUpperCase() === 'END_TURN') {
					endTurn();
				} else if (data.stopReason && data.stopReason.toUpperCase() === 'INTERRUPTED') {
					console.log('Interrupted by user');
					audioPlayer.bargeIn();
				}
			} else if (data.type === 'AUDIO') {
				// When audio content ends, we may need to show user thinking/listening indicator
				if (isStreaming) {
					listening = true;
				}
			}
		});

		// Stream completion event
		socket.on('streamComplete', () => {
			if (isStreaming) {
				stopStreaming();
			}
			statusText = 'Ready';
			statusClass = 'ready';
		});

		// Handle connection status updates
		socket.on('connect', () => {
			statusText = 'Connected to server';
			statusClass = 'connected';
			sessionInitialized = false;
		});

		socket.on('disconnect', () => {
			statusText = 'Disconnected from server';
			statusClass = 'disconnected';
			startButtonEnabled = false;
			stopButtonEnabled = false;
			sessionInitialized = false;
			listening = false;
			thinking = false;
		});

		// Handle errors
		socket.on('error', (error) => {
			console.error('Server error:', error);
			statusText = 'Error: ' + (error.message || JSON.stringify(error).substring(0, 100));
			statusClass = 'error';
			listening = false;
			thinking = false;
		});
	}
</script>

<div id="app">
	<div id="status" class={statusClass}>{statusText}</div>
	<ChatDisplay bind:messages={chatHistory} bind:thinking bind:listening />
	<div id="controls">
		<button id="start" class="button" disabled={!startButtonEnabled} onclick={startStreaming}
			>Start Streaming</button
		>
		<button id="stop" class="button" disabled={!stopButtonEnabled} onclick={stopStreaming}
			>Stop Streaming</button
		>
	</div>
</div>

<style>
	:root {
		font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
		line-height: 1.5;
		font-weight: 400;

		color-scheme: light dark;
		color: rgba(255, 255, 255, 0.87);
		background-color: #242424;

		font-synthesis: none;
		text-rendering: optimizeLegibility;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	
	/* Button styling - combining both versions */
	button,
	.button {
		border-radius: 8px;
		border: 1px solid transparent;
		padding: 0.6em 1.2em;
		font-size: 1em;
		font-weight: 500;
		font-family: inherit;
		cursor: pointer;
		transition: all 0.25s;
	}

	.button {
		padding: 10px 20px;
		margin: 5px;
		border-radius: 4px;
		background-color: #4caf50;
		color: white;
	}

	.button:hover {
		background-color: #45a049;
	}

	button:hover {
		border-color: #646cff;
	}

	button:focus,
	button:focus-visible {
		outline: 4px auto -webkit-focus-ring-color;
	}

	button:disabled,
	.button:disabled {
		background-color: #cccccc;
		cursor: not-allowed;
		opacity: 0.7;
	}

	/* Status styling */
	#status {
		padding: 10px;
		margin: 10px;
		border-radius: 4px;
		position: sticky;
		top: 0;
		z-index: 100;
	}

	.connected {
		background-color: #dff0d8;
		color: #3c763d;
	}

	.disconnected {
		background-color: #f1d79d;
		color: #8a6d3b;
	}

	.error {
		background-color: #fcf8e3;
		color: #a94442;
	}

	/* Message styling */

	.message {
		margin: 10px 0;
		padding: 12px;
		border-radius: 12px;
		position: relative;
		max-width: 70%;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		word-wrap: break-word;
	}

	.message:hover {
		box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
	}

	.user {
		background-color: #e3f2fd;
		/* Pastel light blue */
		color: #333;
		align-self: flex-end;
		/* Align to right */
		border-bottom-right-radius: 3px;
		/* Bubble shape */
		margin-left: auto;
	}

	.assistant {
		background-color: #fce4ec;
		/* Pinkish */
		color: #333;
		align-self: flex-start;
		/* Align to left */
		border-bottom-left-radius: 3px;
		/* Bubble shape */
		margin-right: auto;
	}

	.system {
		background-color: #fff3e0;
		color: #666;
		font-style: italic;
		max-width: 90%;
		align-self: center;
		/* Center system messages */
		text-align: center;
		font-size: 0.9em;
	}

	.role-label {
		font-size: 0.75em;
		color: #666;
		margin-bottom: 4px;
		font-weight: bold;
	}

	.conversation-end {
		background-color: rgba(245, 245, 245, 0.7);
		color: #666;
		font-style: italic;
		padding: 8px 15px;
		border-radius: 20px;
		margin: 15px auto;
		text-align: center;
		max-width: 60%;
		font-size: 0.9em;
	}

	.message-content {
		line-height: 1.4;
	}

	.thinking-dots {
		display: inline-flex;
		gap: 4px;
		align-items: center;
		height: 20px;
		margin-left: 5px;
		vertical-align: middle;
	}

	.thinking-text {
		display: inline-block;
		margin-right: 4px;
		font-style: italic;
	}



	.thinking-dots {
		display: inline-flex;
		gap: 4px;
		align-items: center;
		height: 20px;
		margin-left: 5px;
		vertical-align: middle;
	}

	.thinking-text {
		display: inline-block;
		margin-right: 4px;
		font-style: italic;
	}

	



	@keyframes pulse {
		0%,
		100% {
			transform: scale(0.7);
			opacity: 0.5;
		}

		50% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.message.thinking {
		opacity: 0.7;
	}

	/* Controls styling */
	#controls {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 15px;
		box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: center;
		gap: 10px;
		z-index: 100;
	}

	/* Dark/light mode adaptations */
	@media (prefers-color-scheme: dark) {
		

		.user {
			background-color: #2c3e50;
			color: #e4e4e4;
		}

		.assistant {
			background-color: #4a235a;
			color: #e4e4e4;
		}

		.system {
			background-color: #2d3436;
			color: #b2bec3;
		}

		.conversation-end {
			background-color: rgba(45, 45, 45, 0.7);
			color: #b2bec3;
		}

		#controls {
			background-color: #242424;
		}

		.role-label {
			color: #b2bec3;
		}

		.connected {
			background-color: #264d33;
			color: #a5d6a7;
		}

		.disconnected {
			background-color: #4d3d26;
			color: #ffe082;
		}

		.error {
			background-color: #4d2626;
			color: #ef9a9a;
		}
	}

	@media (prefers-color-scheme: light) {
		:root {
			color: #213547;
			background-color: #ffffff;
		}



		button {
			background-color: #f9f9f9;
		}

	

		#controls {
			background-color: white;
		}
	}

	/* Media Queries for Responsiveness */
	@media (max-width: 768px) {
		.message {
			max-width: 85%;
		}

		.button,
		button {
			padding: 8px 16px;
			font-size: 14px;
		}

		#app {
			padding: 1rem;
		}
	}

	@media (max-width: 480px) {
		.message {
			max-width: 90%;
		}

	
		.button,
		button {
			padding: 8px 12px;
			font-size: 13px;
		}

		#app {
			padding: 0.5rem;
		}
	}
</style>
