
import { AudioPlayer } from './audio-player';
import { arrayBufferToBase64, base64ToFloat32Array } from './util';
import { io, Socket } from 'socket.io-client';
import { PUBLIC_S2S_SERVER } from '$env/static/public';
import type { VChatMessage } from './types';
	
	
const MAGIC_END_CONVERSATION = 'Thank you for your time'; // TODO: add to config and auto add to aisurvey prompt

export class AudioStreamer {

	private socket: Socket;
	private systemPrompt: string = 'You are a helpful assistant.';

	 constructor(sysPrompt:string) {
		this.systemPrompt = sysPrompt;
		let audioPlayer = new AudioPlayer();	

	 
	}
	
	public Initialize()  {
		// Connect to the server
		this.socket = io(PUBLIC_S2S_SERVER);
		InitEventHandlers();
		initAudio();
		const r = this.socket.connect();
	
	};

	public UnInit() {
		this.socket.disconnect
	}






	

	let statusText = $state('Disconnected');
	let statusClass = $state('disconnected');
	let toastId = $state<any>();

	function setStatus(msg: string, level:string, dur: any) {
		if(toastId) {
			toast.dismiss(toastId);
		}
		if(level == 'error') {
			toastId = toast.error(msg, { duration: dur ? dur: Infinity });
		}
		else if(level == 'success') {
			toastId = toast.success(msg, { duration: dur ? dur: Infinity });
		}
		else if(level == 'warning') {
			toastId = toast.warning(msg, { duration: dur ? dur: Infinity });
		}
		else {
			toastId = toast.info(msg, { duration: dur ? dur: Infinity });
		}

	}

	let startButtonEnabled = $state(false);
	let stopButtonEnabled = $state(false);

	// chat display bound vars
	//let chatHistory: ChatMessage[] = $state([]);
	let thinking = $state(false);
	let listening = $state(false);

	function addTextMessage(content: { role: any; message: string }) {
		let updatedChatHistory = [...vChatHistory];
		let lastTurn = updatedChatHistory[updatedChatHistory.length - 1];

		if (lastTurn !== undefined && lastTurn.role === content.role) {
			// Same role, append to the last turn
			updatedChatHistory[updatedChatHistory.length - 1] = {
				...content,
				message: lastTurn.message + ' ' + content.message
			};
		} else {
			// Different role, add a new turn
			updatedChatHistory.push({
				role: content.role,
				message: content.message
			});
		}
		vChatHistory = updatedChatHistory;
	}

	function endTurn() {
		let updatedChatHistory = vChatHistory.map((item) => {
			return {
				...item,
				endOfResponse: true
			};
		});

		vChatHistory = updatedChatHistory;
	}

	function endConversation() {
		let updatedChatHistory = vChatHistory.map((item) => {
			return {
				...item,
				endOfResponse: true
			};
		});

		updatedChatHistory.push({
			role: '',
			message: '',
			endOfResponse: true,
			endOfConversation: true
		});

		vChatHistory = updatedChatHistory;
	}

	// Audio processing variables
	let audioContext: AudioContext;
	let audioStream: MediaStream;
	let isStreaming = false;
	//let processor:ScriptProcessorNode;
	let microphoneWorkletNode: AudioWorkletNode | null = null; // Replaces ScriptProcessorNode
	let sourceNode: MediaStreamAudioSourceNode;
	let transcriptionReceived = false;
	let displayAssistantText = false;
	let role: string;
	const audioPlayer = new AudioPlayer();
	let sessionInitialized = false;

	let samplingRatio = 1;
	const TARGET_SAMPLE_RATE = 16000;
	const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');







	// Custom system prompt - you can modify this
	/*let SYSTEM_PROMPT =
		'You are a friend. The user and you will engage in a spoken ' +
		'dialog exchanging the transcripts of a natural real-time conversation. Keep your responses short, ' +
		'generally two or three sentences for chatty scenarios.';
	*/
	const SYSTEM_PROMPT = `You are a survey agent. The user and you will engage in a spoken 
  conversation where you will ask the user a series of questions to gather information.
  Keep your responses short, generally two or three sentences for chatty scenarios.
  When the user is ready to begin, you will introduce yourself and ask the first question.
  Use the tool to get the questions to ask the user.  Do not ask any questions that are not provided by the tool.
  Always start by using getNextQuestionTool to get the first question.
  `;


// Create microphone worklet processor
	async function createMicrophoneWorklet() {
		const workletCode = `
			class MicrophoneProcessor extends AudioWorkletProcessor {
				constructor() {
					super();
					this.isStreaming = false;
					this.samplingRatio = 1;
					this.isFirefox = false;
					
					// Listen for configuration updates
					this.port.onmessage = (event) => {
						const { type, data } = event.data;
						if (type === 'updateConfig') {
							this.isStreaming = data.isStreaming;
							this.samplingRatio = data.samplingRatio;
							this.isFirefox = data.isFirefox;
						}
					};
				}

				process(inputs, outputs, parameters) {
					if (!this.isStreaming || !inputs[0] || !inputs[0][0]) {
						return true;
					}

					const inputData = inputs[0][0]; // Get first channel
					const numSamples = Math.round(inputData.length / this.samplingRatio);
					const pcmData = this.isFirefox ? new Int16Array(numSamples) : new Int16Array(inputData.length);

					// Convert to 16-bit PCM
					if (this.isFirefox) {
						for (let i = 0; i < numSamples; i++) {
							// NOTE: for firefox the samplingRatio is not 1,
							// so it will downsample by skipping some input samples
							// A better approach is to compute the mean of the samplingRatio samples.
							// or pass through a low-pass filter first
							// But skipping is a preferable low-latency operation
							const sampleIndex = Math.floor(i * this.samplingRatio);
							if (sampleIndex < inputData.length) {
								pcmData[i] = Math.max(-1, Math.min(1, inputData[sampleIndex])) * 0x7fff;
							}
						}
					} else {
						for (let i = 0; i < inputData.length; i++) {
							pcmData[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7fff;
						}
					}

					// Send PCM data to main thread
					this.port.postMessage({
						type: 'audioData',
						data: pcmData.buffer
					});

					return true;
				}
			}

			registerProcessor('microphone-processor', MicrophoneProcessor);
		`;

		const blob = new Blob([workletCode], { type: 'application/javascript' });
		const workletUrl = URL.createObjectURL(blob);
		
		try {
			await audioContext.audioWorklet.addModule(workletUrl);
		} finally {
			URL.revokeObjectURL(workletUrl);
		}
	}



  
	// Initialize WebSocket audio
	async function initAudio() {
		try {
			statusText = 'Requesting microphone access...';
			statusClass = 'connecting';
			setStatus('Requesting microphone access...','info',Infinity);
			
				

			// Request microphone access
			audioStream = await navigator.mediaDevices.getUserMedia({
				audio: {
					echoCancellation: true,
					noiseSuppression: true,
					autoGainControl: true
				}
			});

			if (isFirefox) {
				//firefox doesn't allow audio context have differnt sample rate than what the user media device offers
				audioContext = new AudioContext();
				
			} else {
				audioContext = new AudioContext({
					sampleRate: TARGET_SAMPLE_RATE
				});
			}
			
			//samplingRatio - is only relevant for firefox, for Chromium based browsers, it's always 1
			samplingRatio = audioContext.sampleRate / TARGET_SAMPLE_RATE;
			console.log(
				`Debug AudioContext- sampleRate: ${audioContext.sampleRate} samplingRatio: ${samplingRatio}`
			);

			// Load microphone worklet
			await createMicrophoneWorklet();
			await audioPlayer.start();

			statusText = 'Microphone ready. Click Start to begin.';
			statusClass = 'ready';
			setStatus('Microphone ready. Click Start to begin.','success',30000);
			startButtonEnabled = true;
		} catch (error: any) {
			console.error('Error accessing microphone:', error);
			statusText = 'Error: ' + error.message;
			statusClass = 'error';
			setStatus('Error: ' + error.message,'error',Infinity);
			
		}
	}

	// Initialize the session with Bedrock
	async function initializeSession() {
		if (sessionInitialized) return;

		statusText = 'Initializing session...';
		setStatus('Initializing session...','info',Infinity);
			

		try {
			// Send events in sequence
			socket.emit('promptStart');
			socket.emit('systemPrompt', system_prompt || SYSTEM_PROMPT);
			socket.emit('audioStart');

			// Mark session as initialized
			sessionInitialized = true;
			statusText = 'Session initialized successfully';
			setStatus('Session initialized successfully','success',10000);
		} catch (error:any) {
			console.error('Failed to initialize session:', error);
			statusText = 'Error initializing session';
			statusClass = 'error';
			setStatus('Error: ' + error.message,'error',Infinity);
		}
	}

	async function startStreaming() {
		if (isStreaming) return;

		try {
			// First, make sure the session is initialized
			if (!sessionInitialized) {
				await initializeSession();
			}

			// Create audio source
			sourceNode = audioContext.createMediaStreamSource(audioStream);

			// Create modern AudioWorkletNode instead of deprecated ScriptProcessorNode
			microphoneWorkletNode = new AudioWorkletNode(audioContext, 'microphone-processor', {
				numberOfInputs: 1,
				numberOfOutputs: 0, // No output needed for microphone processing
				channelCount: 1,
				channelCountMode: 'explicit',
				channelInterpretation: 'speakers'
			});

			// Handle messages from worklet
			microphoneWorkletNode.port.onmessage = (event) => {
				const { type, data } = event.data;
				if (type === 'audioData') {
					// Convert to base64 (browser-safe way)
					const base64Data = arrayBufferToBase64(data);
					
					// Send to server
					socket.emit('audioInput', base64Data);
				}
			};

			// Configure worklet
			microphoneWorkletNode.port.postMessage({
				type: 'updateConfig',
				data: {
					isStreaming: true,
					samplingRatio: samplingRatio,
					isFirefox: isFirefox
				}
			});

			// Connect audio graph
			sourceNode.connect(microphoneWorkletNode);

			isStreaming = true;
			startButtonEnabled = false;
			stopButtonEnabled = true;
			statusText = 'Streaming... Speak now';
			statusClass = 'recording';
			setStatus('Listening... Speak now','success', 20000);

			// Show user thinking/listening indicator when starting to record
			transcriptionReceived = false;
			listening = true;
		} catch (error: any) {
			console.error('Error starting recording:', error);
			statusText = 'Error: ' + error.message;
			statusClass = 'error';
			setStatus('Error: ' + error.message,'error',Infinity);
			
			// Fallback to legacy implementation if AudioWorklet fails
			console.warn('AudioWorklet failed, falling back to ScriptProcessorNode');
			await startStreamingLegacy();
		}
	}

	// Fallback legacy implementation
	async function startStreamingLegacy() {
		try {
			if (!sessionInitialized) {
				await initializeSession();
			}

			sourceNode = audioContext.createMediaStreamSource(audioStream);

			// Fallback to ScriptProcessorNode for older browsers
			if (audioContext.createScriptProcessor) {
				const processor = audioContext.createScriptProcessor(512, 1, 1);

				processor.onaudioprocess = (e) => {
					if (!isStreaming) return;

					const inputData = e.inputBuffer.getChannelData(0);
					const numSamples = Math.round(inputData.length / samplingRatio);
					const pcmData = isFirefox ? new Int16Array(numSamples) : new Int16Array(inputData.length);

					// Convert to 16-bit PCM
					if (isFirefox) {
						for (let i = 0; i < inputData.length; i++) {
							pcmData[i] = Math.max(-1, Math.min(1, inputData[i * samplingRatio])) * 0x7fff;
						}
					} else {
						for (let i = 0; i < inputData.length; i++) {
							pcmData[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7fff;
						}
					}

					// Convert to base64 (browser-safe way)
					const base64Data = arrayBufferToBase64(pcmData.buffer);

					// Send to server
					socket.emit('audioInput', base64Data);
				};

				sourceNode.connect(processor);
				processor.connect(audioContext.destination);
			}

			isStreaming = true;
			startButtonEnabled = false;
			stopButtonEnabled = true;
			statusText = 'Streaming... Speak now (legacy mode)';
			statusClass = 'recording';
			setStatus('Listening... Speak now','success', 20000);

			transcriptionReceived = false;
			listening = true;
		} catch (error: any) {
			console.error('Error starting legacy recording:', error);
			statusText = 'Error: ' + error.message;
			statusClass = 'error';
			setStatus('Error: ' + error.message,'error',Infinity);
		}
	}

	function stopStreaming() {
		if (!isStreaming) return;

		isStreaming = false;

		// Clean up audio processing
		if (microphoneWorkletNode) {
			// Stop streaming in worklet
			microphoneWorkletNode.port.postMessage({
				type: 'updateConfig',
				data: {
					isStreaming: false,
					samplingRatio: samplingRatio,
					isFirefox: isFirefox
				}
			});
			microphoneWorkletNode.disconnect();
			microphoneWorkletNode = null;
		}

		if (sourceNode) {
			sourceNode.disconnect();
		}

		startButtonEnabled = true;
		stopButtonEnabled = false;
		statusText = 'Processing...';
		statusClass = 'processing';
		setStatus('Processing...','warning',10000);

		audioPlayer.stop();
		// Tell server to finalize processing
		socket.emit('stopAudio');

		// End the current turn in chat history
		endTurn();
	}


	

	// Process message data and add to chat history
	function handleTextOutput(data: { role: any; content: any; }) {
		console.log('Processing text output:', data);
		if (data.content) {
			const messageData = {
				role: data.role,
				message: data.content
			};
			addTextMessage(messageData);
			if(messageData.message.toLowerCase().includes(MAGIC_END_CONVERSATION.toLowerCase())) {
				stopStreaming();
			}
		}
	}

	// EVENT HANDLERS
	// --------------
	function InitEventHandlers() {
		// Handle content start from the server
		socket.on('contentStart', (data) => {
			console.log('Content start received:', data);

			if (data.type === 'TEXT') {
				// Below update will be enabled when role is moved to the contentStart
				role = data.role;
				if (data.role === 'USER') {
					// When user's text content starts, hide user thinking indicator
					listening = false;
				} else if (data.role === 'ASSISTANT') {
					// When assistant's text content starts, hide assistant thinking indicator
					thinking = false;
					let isSpeculative = false;
					try {
						if (data.additionalModelFields) {
							const additionalFields = JSON.parse(data.additionalModelFields);
							isSpeculative = additionalFields.generationStage === 'SPECULATIVE';
							if (isSpeculative) {
								console.log('Received speculative content');
								displayAssistantText = true;
							} else {
								displayAssistantText = false;
							}
						}
					} catch (e) {
						console.error('Error parsing additionalModelFields:', e);
					}
				}
			} else if (data.type === 'AUDIO') {
				// When audio content starts, we may need to show user thinking/listening indicator
				if (isStreaming) {
					listening = true;
				}
			}
		});

		// Handle text output from the server
		socket.on('textOutput', (data) => {
			console.log('Received text output:', data);

			if (role === 'USER') {
				// When user text is received, show thinking indicator for assistant response
				transcriptionReceived = true;
				//listening = false();

				// Add user message to chat
				handleTextOutput({
					role: data.role,
					content: data.content
				});

				// Show assistant thinking indicator after user text appears
				thinking = true;
			} else if (role === 'ASSISTANT') {
				//thinkng = false();
				if (displayAssistantText) {
					handleTextOutput({
						role: data.role,
						content: data.content
					});
				}
			}
		});

		// Handle audio output
		socket.on('audioOutput', (data) => {
			if (data.content) {
				try {
					const audioData = base64ToFloat32Array(data.content);
					audioPlayer.playAudio(audioData);
				} catch (error) {
					console.error('Error processing audio data:', error);
				}
			}
		});

		// Handle content end events
		socket.on('contentEnd', (data) => {
			console.log('Content end received:', data);

			if (data.type === 'TEXT') {
				if (role === 'USER') {
					// When user's text content ends, make sure assistant thinking is shown
					listening = false;
					thinking = true;
				} else if (role === 'ASSISTANT') {
					// When assistant's text content ends, prepare for user input in next turn
					thinking = false;
				}

				// Handle stop reasons
				if (data.stopReason && data.stopReason.toUpperCase() === 'END_TURN') {
					endTurn();
				} else if (data.stopReason && data.stopReason.toUpperCase() === 'INTERRUPTED') {
					console.log('Interrupted by user');
					audioPlayer.bargeIn();
				}
			} else if (data.type === 'AUDIO') {
				// When audio content ends, we may need to show user thinking/listening indicator
				if (isStreaming) {
					listening = true;
				}
			}
		});

		// Stream completion event
		socket.on('streamComplete', () => {
			if (isStreaming) {
				stopStreaming();
			}
			statusText = 'Ready';
			statusClass = 'ready';
			setStatus('Ready','info',10000);
			
		});

		// Handle connection status updates
		socket.on('connect', () => {
			statusText = 'Connected to server';
			statusClass = 'connected';
			setStatus('Connected','success',10000);
			
			sessionInitialized = false;
		});

		socket.on('disconnect', () => {
			statusText = 'Disconnected from server';
			statusClass = 'disconnected';
			setStatus('Disconnected','warning',10000);
			startButtonEnabled = false;
			stopButtonEnabled = false;
			sessionInitialized = false;
			listening = false;
			thinking = false;
		});

		// Handle errors
		socket.on('error', (error) => {
			console.error('Server error:', error);
			statusText = 'Error: ' + (error.message || JSON.stringify(error).substring(0, 100));
			statusClass = 'error';
			setStatus('Error: ' + error.message,'error',Infinity);
			listening = false;
			thinking = false;
		});
	}

}