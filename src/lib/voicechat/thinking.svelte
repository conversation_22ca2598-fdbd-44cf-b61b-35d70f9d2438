<script lang="ts">
	// Component props
	let { text } = $props<{
		text: string;
	}>();
</script>

<div class="flex items-center space-x-1">
	<span class="text-sm text-gray-500">{text}</span>
	<div class="flex space-x-1">
		<div class="thinking-dot"></div>
		<div class="thinking-dot" style="animation-delay: 0.2s;"></div>
		<div class="thinking-dot" style="animation-delay: 0.4s;"></div>
	</div>
</div>

<style>
	.thinking-dot {
		width: 4px;
		height: 4px;
		background-color: #9ca3af;
		border-radius: 50%;
		animation: thinking-pulse 1.4s ease-in-out infinite both;
	}

	@keyframes thinking-pulse {
		0%,
		80%,
		100% {
			transform: scale(0);
			opacity: 0.5;
		}
		40% {
			transform: scale(1);
			opacity: 1;
		}
	}
</style>
