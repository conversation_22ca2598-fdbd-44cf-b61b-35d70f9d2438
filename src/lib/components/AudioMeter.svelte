<script lang="ts">
	import { onMount } from 'svelte';
	import type { AudioMeterData } from '../audioManager.js';
	import { audioManager } from '../audioManager.js';

	interface Props {
		orientation?: 'horizontal' | 'vertical';
		size?: 'small' | 'medium' | 'large';
		showPeak?: boolean;
		showRMS?: boolean;
		showNumbers?: boolean;
	}

	let {
		orientation = 'horizontal',
		size = 'medium',
		showPeak = true,
		showRMS = true,
		showNumbers = true
	}: Props = $props();

	let meterData = $state<AudioMeterData>({
		level: 0,
		peak: 0,
		rms: 0,
		clipping: false
	});

	let peakHold = $state(0);
	let peakHoldTimeout: number | null = null;

	let unsubscribe: (() => void) | null = null;

	onMount(() => {
		unsubscribe = audioManager.onMeterUpdate((data) => {
			meterData = data;
			
			// Peak hold logic
			if (data.peak > peakHold) {
				peakHold = data.peak;
				if (peakHoldTimeout) {
					clearTimeout(peakHoldTimeout);
				}
				peakHoldTimeout = setTimeout(() => {
					peakHold = 0;
				}, 2000);
			}
		});
	});

	$effect(() => {
		return () => {
			if (unsubscribe) {
				unsubscribe();
			}
			if (peakHoldTimeout) {
				clearTimeout(peakHoldTimeout);
			}
		};
	});

	// Calculate meter dimensions based on size
	const dimensions = {
		small: orientation === 'horizontal' ? 'h-2 w-48' : 'w-2 h-48',
		medium: orientation === 'horizontal' ? 'h-4 w-64' : 'w-4 h-64',
		large: orientation === 'horizontal' ? 'h-6 w-80' : 'w-6 h-80'
	};

	// Convert level to percentage (derived state)
	let levelPercentage = $derived(Math.min(100, meterData.level * 100));
	let peakPercentage = $derived(Math.min(100, meterData.peak * 100));
	let rmsPercentage = $derived(Math.min(100, meterData.rms * 100));
	let peakHoldPercentage = $derived(Math.min(100, peakHold * 100));

	// Get color based on level
	function getLevelColor(level: number): string {
		if (level > 90) return 'bg-red-500';
		if (level > 75) return 'bg-yellow-400';
		if (level > 50) return 'bg-green-400';
		return 'bg-green-500';
	}
</script>

<div class="flex flex-col gap-2">
	<!-- Meter Display -->
	<div class="relative {dimensions[size]} bg-audio-surface dark:bg-audio-surface 
	            border border-audio-border dark:border-audio-border 
	            rounded overflow-hidden">
		<!-- Background -->
		<div class="absolute inset-0 bg-gradient-to-r from-green-900/30 via-yellow-900/30 to-red-900/30 dark:from-green-200/30 light:via-yellow-200/30 dark:to-red-200/30"></div>
		
		<!-- RMS Level (background level) -->
		{#if showRMS}
			<div 
				class="absolute {orientation === 'horizontal' ? 'inset-y-0 left-0' : 'inset-x-0 bottom-0'} 
				       bg-green-600/50 transition-all duration-75"
				style="{orientation === 'horizontal' ? 'width' : 'height'}: {rmsPercentage}%"
			></div>
		{/if}
		
		<!-- Peak Level (main level) -->
		<div 
			class="absolute {orientation === 'horizontal' ? 'inset-y-0 left-0' : 'inset-x-0 bottom-0'} 
			       {getLevelColor(levelPercentage)} transition-all duration-75"
			style="{orientation === 'horizontal' ? 'width' : 'height'}: {levelPercentage}%"
		></div>
		
		<!-- Peak Hold -->
		{#if showPeak && peakHoldPercentage > 0}
			<div 
				class="absolute {orientation === 'horizontal' ? 'inset-y-0 w-1' : 'inset-x-0 h-1'} 
				       bg-white"
				style="{orientation === 'horizontal' ? 'left' : 'bottom'}: {peakHoldPercentage}%"
			></div>
		{/if}
		
		<!-- Clipping Indicator -->
		{#if meterData.clipping}
			<div class="absolute inset-0 bg-red-500 animate-pulse opacity-50"></div>
		{/if}
	</div>
	
	<!-- Numerical Display -->
	{#if showNumbers}
		<div class="flex gap-4 text-xs font-mono text-audio-muted dark:text-audio-muted">
			<span>RMS: {(meterData.rms * 100).toFixed(1)}%</span>
			<span>Peak: {(meterData.peak * 100).toFixed(1)}%</span>
			{#if meterData.clipping}
				<span class="text-red-400 dark:text-red-600 font-bold">CLIP</span>
			{/if}
		</div>
	{/if}
</div>


<style>
	/* Custom audio meter styles */
.audio-meter {
	background: linear-gradient(
		to right,
		rgb(34 197 94) 0%,
		rgb(74 222 128) 50%,
		rgb(250 204 21) 75%,
		rgb(239 68 68) 90%,
		rgb(220 38 38) 100%
	);
}

.audio-meter-bg {
	background: var(--color-audio-surface);
	border: 1px solid var(--color-audio-border);
}

.light .audio-meter-bg {
	background: var(--color-audio-surface-light);
	border: 1px solid var(--color-audio-border-light);
}

/* VU Meter specific styles */
.vu-meter {
	background: radial-gradient(
		circle at center,
		var(--color-audio-surface) 0%,
		var(--color-audio-bg) 100%
	);
}

.light .vu-meter {
	background: radial-gradient(
		circle at center,
		var(--color-audio-surface-light) 0%,
		var(--color-audio-bg-light) 100%
	);
}

</style>