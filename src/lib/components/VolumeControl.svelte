<script lang="ts">
	import { Volume2, VolumeX, Mic, MicOff } from '@lucide/svelte';

	interface Props {
		type: 'input' | 'output';
		value: number;
		muted?: boolean;
		onValueChange?: (value: number) => void;
		onMuteToggle?: (muted: boolean) => void;
		min?: number;
		max?: number;
		step?: number;
	}

	let {
		type,
		value = 1.0,
		muted = false,
		onValueChange,
		onMuteToggle,
		min = 0,
		max = 2,
		step = 0.01
	}: Props = $props();

	function handleSliderChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const newValue = parseFloat(target.value);
		onValueChange?.(newValue);
	}

	function toggleMute() {
		const newMuted = !muted;
		onMuteToggle?.(newMuted);
	}

	// Convert gain to percentage for display
	let percentage = $derived(Math.round((value / max) * 100));
	
	// Convert gain to dB for display
	let dB = $derived(value > 0 ? Math.round(20 * Math.log10(value)) : -Infinity);

	// Get slider color based on level
	function getSliderColor(val: number): string {
		if (muted) return 'opacity-50';
		if (val > 1.5) return 'text-red-400';
		if (val > 1.2) return 'text-yellow-400';
		return 'text-audio-accent';
	}
</script>

<div class="flex flex-col gap-3">
	<div class="flex items-center justify-between">
		<label for="volume-slider-{type}" class="text-sm font-medium text-audio-text dark:text-audio-text flex items-center gap-2">
			{#if type === 'input'}
				{#if muted}
					<MicOff class="w-4 h-4" />
				{:else}
					<Mic class="w-4 h-4" />
				{/if}
			{:else}
				{#if muted}
					<VolumeX class="w-4 h-4" />
				{:else}
					<Volume2 class="w-4 h-4" />
				{/if}
			{/if}
			{type === 'input' ? 'Input Gain' : 'Output Gain'}
		</label>
		
		<button
			type="button"
			onclick={toggleMute}
			class="p-1 rounded hover:bg-audio-border dark:hover:bg-audio-border 
			       transition-colors 
			       {muted ? 'text-red-400 dark:text-red-600' : 'text-audio-text dark:text-audio-text'}"
			title={muted ? 'Unmute' : 'Mute'}
		>
			{#if type === 'input'}
				{#if muted}
					<MicOff class="w-4 h-4" />
				{:else}
					<Mic class="w-4 h-4" />
				{/if}
			{:else}
				{#if muted}
					<VolumeX class="w-4 h-4" />
				{:else}
					<Volume2 class="w-4 h-4" />
				{/if}
			{/if}
		</button>
	</div>

	<div class="flex items-center gap-3">
		<span class="text-xs text-audio-muted dark:text-audio-muted min-w-8">0%</span>
		
		<div class="flex-1 relative">
			<input
				id="volume-slider-{type}"
				type="range"
				{min}
				{max}
				{step}
				{value}
				oninput={handleSliderChange}
				class="w-full h-2 bg-audio-border dark:bg-audio-border 
				       rounded-lg appearance-none cursor-pointer 
				       {getSliderColor(value)} {muted ? 'opacity-50' : ''}"
				disabled={muted}
			/>
			
			<!-- Unity gain marker -->
			<div 
				class="absolute top-1/2 w-0.5 h-4 bg-audio-muted dark:bg-audio-muted 
				       transform -translate-y-1/2 pointer-events-none"
				style="left: {(1 / max) * 100}%"
			></div>
		</div>
		
		<span class="text-xs text-audio-muted dark:text-audio-muted min-w-12">200%</span>
	</div>

	<div class="flex justify-between text-xs">
		<span class="text-audio-muted dark:text-audio-muted">
			{percentage}%
		</span>
		<span class="text-audio-muted dark:text-audio-muted">
			{dB === -Infinity ? '-∞' : dB >= 0 ? `+${dB}` : dB} dB
		</span>
		<span class="text-audio-muted dark:text-audio-muted">
			{value.toFixed(2)}x
		</span>
	</div>

	{#if value > 1.2}
		<div class="text-xs text-yellow-400 dark:text-yellow-600 flex items-center gap-1">
			⚠️ High gain may cause distortion
		</div>
	{/if}

	{#if value > 1.5}
		<div class="text-xs text-red-400 dark:text-red-600 flex items-center gap-1">
			🚨 Very high gain - clipping likely
		</div>
	{/if}
</div>
